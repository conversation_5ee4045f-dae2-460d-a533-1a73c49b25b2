from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import re
from datetime import datetime

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        # 获取请求数据
        data = request.get_json()
        user_message = data.get('message', '')
        detection_results = data.get('detectionResults', [])
        
        # 生成AI响应
        ai_response = generate_chat_response(user_message, detection_results)
        
        return jsonify({'reply': ai_response})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_chat_response(user_message, detection_results):
    """生成聊天响应"""
    # 简单的规则基础响应生成
    response = ""
    
    # 如果有检测结果，基于检测结果生成响应
    if detection_results:
        # 统计检测到的目标类别
        class_counts = {}
        for det in detection_results:
            cls = det.get('class', '未知')
            class_counts[cls] = class_counts.get(cls, 0) + 1
        
        # 生成目标统计信息
        object_summary = "，".join([f"{count}个{cls}" for cls, count in class_counts.items()])
        
        # 根据用户消息内容生成不同响应
        if any(keyword in user_message.lower() for keyword in ['数量', '多少', '统计']):
            response = f"根据检测结果，图像中包含{object_summary}。"
        elif any(keyword in user_message.lower() for keyword in ['位置', '坐标']):
            # 提供第一个检测目标的位置信息
            if detection_results:
                first_obj = detection_results[0]
                box = first_obj.get('box', {})
                response = f"检测到的第一个{first_obj.get('class', '目标')}位于坐标(xmin={box.get('xmin', 0)}, ymin={box.get('ymin', 0)}, xmax={box.get('xmax', 0)}, ymax={box.get('ymax', 0)})。"
            else:
                response = "未检测到任何目标。"
        else:
            response = f"检测到{len(detection_results)}个目标，包括{object_summary}。您能提供更多关于这些目标的信息吗？"
    else:
        # 没有检测结果时的默认响应
        if any(keyword in user_message.lower() for keyword in ['你好', 'hello', 'hi']):
            response = "您好！我是多模态图像语义理解系统的AI助手。请上传一张图片并进行目标检测，然后我们可以进一步讨论检测结果。"
        elif any(keyword in user_message.lower() for keyword in ['帮助', 'help']):
            response = "我可以帮助您分析图像中的目标。请上传一张图片，点击'开始检测理解'按钮，然后我们可以讨论检测结果。"
        else:
            response = "我已经收到您的消息。请上传一张图片并进行目标检测，这样我就能更好地帮助您分析图像内容。"
    
    return response

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
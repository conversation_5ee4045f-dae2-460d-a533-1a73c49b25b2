import requests
import os

# 测试Qwen分析API
url = 'http://localhost:5000/api/qwen-analyze'

# 准备测试数据
# 请确保test_image.jpg文件存在于项目根目录
image_path = '../demo.png'
prompt = '请分析这张图片中有什么内容？'

# 检查测试图片是否存在
if os.path.exists(image_path):
    with open(image_path, 'rb') as f:
        files = {'image': f}
        data = {'prompt': prompt}
        
        # 发送请求
        response = requests.post(url, files=files, data=data)
        
        # 打印响应
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
else:
    print(f"测试图片不存在: {image_path}")
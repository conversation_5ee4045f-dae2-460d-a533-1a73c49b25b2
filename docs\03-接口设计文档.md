# 多模态图像语义理解系统 - 接口设计文档

## 1. API接口概述

本系统采用RESTful API设计风格，所有接口均基于HTTP协议，支持JSON和multipart/form-data数据格式。API服务运行在Flask框架上，默认端口5000，支持跨域请求。

### 1.1 基础信息
- **基础URL**: `http://localhost:5000`
- **API版本**: v1
- **数据格式**: JSON / multipart/form-data
- **字符编码**: UTF-8
- **跨域支持**: 已启用CORS

### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.3 错误响应格式
```json
{
  "error": "错误描述",
  "details": "详细错误信息",
  "code": 400
}
```

## 2. 核心API接口规范

### 2.1 目标检测接口

#### 2.1.1 接口信息
- **URL**: `/api/detect`
- **方法**: POST
- **内容类型**: multipart/form-data
- **功能**: 对上传的图像或视频进行目标检测和态势分析

#### 2.1.2 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| image | File | 否* | 图像文件(JPEG、PNG、WebP等) |
| video | File | 否* | 视频文件(MP4、AVI、MOV等) |
| prompt | String | 是 | 检测提示词，描述要检测的目标 |
| selectedTopics | String | 否 | JSON字符串，态势分析主题列表 |
| reconnaissance_center | String | 否 | JSON字符串，侦察区域中心坐标 |

*注：image和video至少提供一个

#### 2.1.3 请求示例
```bash
curl -X POST http://localhost:5000/api/detect \
  -F "image=@example.jpg" \
  -F "prompt=检测图像中的人员和车辆" \
  -F "selectedTopics=[\"ZZ主体\",\"交通状况\"]" \
  -F "reconnaissance_center={\"longitude\":116.4074,\"latitude\":39.9042}"
```

#### 2.1.4 响应格式
```json
{
  "detections": [
    {
      "target_id": "uuid-string",
      "class": "person",
      "score": 0.95,
      "box": {
        "xmin": 100,
        "ymin": 150,
        "xmax": 300,
        "ymax": 400
      },
      "reconnaissance_center": {
        "longitude": 116.4074,
        "latitude": 39.9042
      }
    }
  ],
  "situation_analysis": {
    "summary": "检测到1个人员目标",
    "situation_details": [
      {
        "topic": "ZZ主体",
        "situation": "图像中包含一名人员"
      }
    ]
  },
  "result_image": "hex-encoded-image-data"
}
```

### 2.2 智能对话接口

#### 2.2.1 接口信息
- **URL**: `/api/chat`
- **方法**: POST
- **内容类型**: multipart/form-data
- **功能**: 基于图像和检测结果的智能问答

#### 2.2.2 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| message | String | 是 | 用户输入的消息内容 |
| image | File | 否 | 图像文件(可选) |
| detectionResults | String | 否 | JSON字符串，检测结果数据 |

#### 2.2.3 请求示例
```bash
curl -X POST http://localhost:5000/api/chat \
  -F "message=图像中有多少个人？" \
  -F "image=@example.jpg" \
  -F "detectionResults=[{\"class\":\"person\",\"score\":0.95}]"
```

#### 2.2.4 响应格式
```json
{
  "reply": "根据检测结果，图像中包含1个人员目标。"
}
```

### 2.3 Qwen多模态分析接口

#### 2.3.1 接口信息
- **URL**: `/api/qwen-analyze`
- **方法**: POST
- **内容类型**: multipart/form-data
- **功能**: 调用Qwen2.5-VL模型进行图像分析

#### 2.3.2 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| image | File | 是 | 图像文件 |
| prompt | String | 是 | 分析提示词 |

#### 2.3.3 请求示例
```bash
curl -X POST http://localhost:5000/api/qwen-analyze \
  -F "image=@example.jpg" \
  -F "prompt=分析图像中的主要内容"
```

#### 2.3.4 响应格式
```json
{
  "analysis": "图像显示了一个城市街道场景，包含建筑物、车辆和行人。"
}
```

### 2.4 提示词模板管理接口

#### 2.4.1 获取提示词模板列表
- **URL**: `/api/prompts`
- **方法**: GET
- **功能**: 获取所有提示词模板

**响应格式**:
```json
[
  {
    "id": "default",
    "name": "默认提示词",
    "prompt": "检测图像中的所有目标"
  }
]
```

#### 2.4.2 创建提示词模板
- **URL**: `/api/prompts`
- **方法**: POST
- **内容类型**: application/json

**请求格式**:
```json
{
  "name": "自定义模板",
  "prompt": "检测特定目标"
}
```

**响应格式**:
```json
{
  "id": "generated-uuid",
  "name": "自定义模板",
  "prompt": "检测特定目标"
}
```

#### 2.4.3 更新提示词模板
- **URL**: `/api/prompts/{template_id}`
- **方法**: PUT
- **内容类型**: application/json

#### 2.4.4 删除提示词模板
- **URL**: `/api/prompts/{template_id}`
- **方法**: DELETE

### 2.5 视频服务接口

#### 2.5.1 接口信息
- **URL**: `/api/video/{video_path}`
- **方法**: GET
- **功能**: 提供视频文件流服务

#### 2.5.2 响应格式
- 直接返回视频文件流
- 支持Range请求(部分内容)
- 自动设置正确的Content-Type

## 3. 错误处理机制

### 3.1 HTTP状态码规范

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 400 | 请求错误 | 参数错误、格式错误 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部处理错误 |

### 3.2 错误响应示例

#### 3.2.1 参数错误 (400)
```json
{
  "error": "未提供图像文件",
  "details": "image参数为必填项",
  "code": 400
}
```

#### 3.2.2 文件格式错误 (400)
```json
{
  "error": "不支持的文件格式",
  "details": "仅支持JPEG、PNG、WebP格式",
  "code": 400
}
```

#### 3.2.3 模型处理错误 (500)
```json
{
  "error": "目标检测失败",
  "details": "模型推理过程中发生错误",
  "code": 500
}
```

#### 3.2.4 API调用错误 (500)
```json
{
  "error": "Qwen分析失败",
  "details": "API密钥无效或网络连接失败",
  "code": 500
}
```

### 3.3 错误处理策略

#### 3.3.1 输入验证
- 文件类型验证
- 文件大小限制
- 参数格式检查
- 必填参数验证

#### 3.3.2 业务逻辑错误
- 模型加载失败
- 推理过程异常
- 结果处理错误
- 外部API调用失败

#### 3.3.3 系统级错误
- 内存不足
- 磁盘空间不足
- 网络连接问题
- 服务不可用

## 4. 接口安全性设计

### 4.1 输入安全

#### 4.1.1 文件上传安全
- 文件类型白名单验证
- 文件大小限制(默认16MB)
- 文件内容检查
- 恶意文件检测

#### 4.1.2 参数验证
- SQL注入防护
- XSS攻击防护
- 参数长度限制
- 特殊字符过滤

### 4.2 API访问控制

#### 4.2.1 跨域配置
```python
CORS(app, origins=["http://localhost:3000"])
```

#### 4.2.2 请求频率限制
- 单IP请求频率限制
- 文件上传频率限制
- API调用次数统计

### 4.3 数据安全

#### 4.3.1 敏感信息保护
- API密钥环境变量存储
- 日志脱敏处理
- 临时文件清理

#### 4.3.2 传输安全
- HTTPS支持(生产环境)
- 请求数据加密
- 响应数据压缩

## 5. API性能优化

### 5.1 缓存策略

#### 5.1.1 模型缓存
- 预加载常用模型
- 模型实例复用
- 内存管理优化

#### 5.1.2 结果缓存
- 检测结果临时缓存
- 图像处理结果缓存
- API响应缓存

### 5.2 并发处理

#### 5.2.1 异步处理
- 长时间任务异步化
- 任务队列管理
- 进度状态查询

#### 5.2.2 资源管理
- 连接池管理
- 内存使用监控
- GPU资源调度

### 5.3 响应优化

#### 5.3.1 数据压缩
- JSON响应压缩
- 图像数据优化
- 分页数据返回

#### 5.3.2 传输优化
- 流式数据传输
- 断点续传支持
- CDN加速(如适用)

## 6. API版本管理

### 6.1 版本策略
- 语义化版本号(v1.0.0)
- 向后兼容原则
- 废弃接口通知机制

### 6.2 版本控制
- URL路径版本控制
- 请求头版本标识
- 版本映射配置

### 6.3 文档维护
- API变更日志
- 版本差异说明
- 迁移指南提供

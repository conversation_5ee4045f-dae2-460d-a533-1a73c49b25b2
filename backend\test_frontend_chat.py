import requests
import json

# 测试前端聊天功能
url = 'http://localhost:5000/api/chat'

# 测试用例1: 问候
print("=== 测试用例1: 问候 ===")
data1 = {
    "message": "你好",
    "detectionResults": [
        {
            "class": "person",
            "score": 0.95,
            "box": {
                "xmin": 100,
                "ymin": 150,
                "xmax": 300,
                "ymax": 400
            }
        },
        {
            "class": "car",
            "score": 0.87,
            "box": {
                "xmin": 400,
                "ymin": 200,
                "xmax": 600,
                "ymax": 350
            }
        }
    ]
}

try:
    # 发送POST请求
    response = requests.post(url, json=data1)
    
    # 打印响应
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
except Exception as e:
    print(f"Error: {e}")

# 测试用例2: 询问目标数量
print("\n=== 测试用例2: 询问目标数量 ===")
data2 = {
    "message": "图像中有多少个目标？",
    "detectionResults": [
        {
            "class": "person",
            "score": 0.95,
            "box": {
                "xmin": 100,
                "ymin": 150,
                "xmax": 300,
                "ymax": 400
            }
        },
        {
            "class": "car",
            "score": 0.87,
            "box": {
                "xmin": 400,
                "ymin": 200,
                "xmax": 600,
                "ymax": 350
            }
        }
    ]
}

try:
    # 发送POST请求
    response = requests.post(url, json=data2)
    
    # 打印响应
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
except Exception as e:
    print(f"Error: {e}")

# 测试用例3: 询问目标位置
print("\n=== 测试用例3: 询问目标位置 ===")
data3 = {
    "message": "第一个检测到的目标的位置在哪里？",
    "detectionResults": [
        {
            "class": "person",
            "score": 0.95,
            "box": {
                "xmin": 100,
                "ymin": 150,
                "xmax": 300,
                "ymax": 400
            }
        }
    ]
}

try:
    # 发送POST请求
    response = requests.post(url, json=data3)
    
    # 打印响应
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
except Exception as e:
    print(f"Error: {e}")

# 测试用例4: 没有检测结果
print("\n=== 测试用例4: 没有检测结果 ===")
data4 = {
    "message": "你好",
    "detectionResults": []
}

try:
    # 发送POST请求
    response = requests.post(url, json=data4)
    
    # 打印响应
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
except Exception as e:
    print(f"Error: {e}")
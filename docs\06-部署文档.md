# 多模态图像语义理解系统 - 部署文档

## 1. 部署概述

本文档详细描述了多模态图像语义理解系统的部署流程，包括环境配置、依赖安装、配置文件设置和系统启动等步骤。系统采用前后端分离架构，需要分别部署React前端应用和Flask后端服务。

### 1.1 系统架构
- **前端**: React + TypeScript (端口3000)
- **后端**: Flask + Python (端口5000)
- **AI模型**: GroundingDINO + Qwen2.5-VL
- **数据库**: 文件系统存储(JSON)

### 1.2 部署模式
- **开发环境**: 本地开发调试
- **测试环境**: 单机部署测试
- **生产环境**: 服务器部署(可选容器化)

## 2. 环境配置要求

### 2.1 硬件要求

#### 2.1.1 最低配置
- **CPU**: 4核心 Intel i5 或 AMD Ryzen 5
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间
- **GPU**: 可选，NVIDIA GTX 1060 或更高(推荐)

#### 2.1.2 推荐配置
- **CPU**: 8核心 Intel i7 或 AMD Ryzen 7
- **内存**: 16GB RAM
- **存储**: 50GB SSD
- **GPU**: NVIDIA RTX 3060 或更高
- **网络**: 稳定的互联网连接(用于API调用)

### 2.2 软件要求

#### 2.2.1 操作系统
- **Windows**: Windows 10/11 (64位)
- **macOS**: macOS 10.15+ (Catalina或更高)
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 10+

#### 2.2.2 运行时环境
- **Python**: 3.8+ (推荐3.9或3.10)
- **Node.js**: 16.0+ (推荐18.x LTS)
- **npm**: 8.0+ (随Node.js安装)
- **Git**: 2.20+ (用于代码管理)

#### 2.2.3 可选组件
- **CUDA**: 11.0+ (如使用GPU加速)
- **Docker**: 20.0+ (容器化部署)
- **Nginx**: 1.18+ (生产环境反向代理)

## 3. 依赖安装步骤

### 3.1 系统环境准备

#### 3.1.1 Windows环境
```bash
# 1. 安装Python 3.9
# 从 https://python.org 下载并安装

# 2. 安装Node.js
# 从 https://nodejs.org 下载并安装LTS版本

# 3. 验证安装
python --version
node --version
npm --version

# 4. 安装Git
# 从 https://git-scm.com 下载并安装
```

#### 3.1.2 Linux环境(Ubuntu)
```bash
# 1. 更新系统包
sudo apt update && sudo apt upgrade -y

# 2. 安装Python 3.9
sudo apt install python3.9 python3.9-pip python3.9-venv -y

# 3. 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y

# 4. 安装Git
sudo apt install git -y

# 5. 验证安装
python3.9 --version
node --version
npm --version
```

#### 3.1.3 macOS环境
```bash
# 1. 安装Homebrew (如未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. 安装Python
brew install python@3.9

# 3. 安装Node.js
brew install node@18

# 4. 安装Git
brew install git

# 5. 验证安装
python3 --version
node --version
npm --version
```

### 3.2 项目代码获取

```bash
# 1. 克隆项目代码
git clone https://github.com/your-repo/mllmjiuhang.git
cd mllmjiuhang

# 2. 查看项目结构
ls -la
```

### 3.3 后端依赖安装

#### 3.3.1 创建Python虚拟环境
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 验证虚拟环境
which python
```

#### 3.3.2 安装Python依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 验证关键依赖
python -c "import torch; print(torch.__version__)"
python -c "import transformers; print(transformers.__version__)"
python -c "import flask; print(flask.__version__)"
```

#### 3.3.3 安装额外依赖(如需要)
```bash
# 如果使用GPU，安装CUDA版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装DashScope SDK (用于Qwen API)
pip install dashscope
```

### 3.4 前端依赖安装

```bash
# 进入前端目录
cd ../frontend

# 安装npm依赖
npm install

# 验证安装
npm list --depth=0

# 可选：使用yarn替代npm
# npm install -g yarn
# yarn install
```

### 3.5 AI模型下载

#### 3.5.1 GroundingDINO模型
```bash
# 创建模型目录
mkdir -p ../models/grounding-dino-tiny

# 下载模型文件(需要HuggingFace访问)
# 方法1: 使用git lfs
cd ../models
git lfs clone https://huggingface.co/IDEA-Research/grounding-dino-tiny

# 方法2: 手动下载
# 从 https://huggingface.co/IDEA-Research/grounding-dino-tiny 下载所有文件
# 放置到 models/grounding-dino-tiny/ 目录下
```

#### 3.5.2 验证模型文件
```bash
# 检查模型文件
ls -la models/grounding-dino-tiny/
# 应包含: config.json, pytorch_model.bin, preprocessor_config.json 等文件
```

## 4. 配置文件说明

### 4.1 后端环境配置

#### 4.1.1 创建.env文件
```bash
# 在backend目录下创建.env文件
cd backend
touch .env  # Linux/macOS
# echo. > .env  # Windows
```

#### 4.1.2 .env文件内容
```bash
# Qwen API配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# 模型配置
MODEL_PATH=../models/grounding-dino-tiny
DEVICE=cuda  # 或 cpu

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=temp

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
```

#### 4.1.3 获取Qwen API密钥
```bash
# 1. 访问阿里云DashScope控制台
# https://dashscope.console.aliyun.com/

# 2. 注册/登录账号

# 3. 创建API密钥

# 4. 将密钥填入.env文件的QWEN_API_KEY字段
```

### 4.2 前端配置

#### 4.2.1 package.json配置
```json
{
  "name": "frontend",
  "proxy": "http://localhost:5000",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test"
  }
}
```

#### 4.2.2 环境变量配置(可选)
```bash
# 在frontend目录创建.env文件
cd frontend
echo "REACT_APP_API_URL=http://localhost:5000" > .env
echo "REACT_APP_VERSION=1.0.0" >> .env
```

### 4.3 提示词模板配置

#### 4.3.1 prompt_templates.json
```json
[
  {
    "id": "default",
    "name": "默认提示词",
    "prompt": "检测图像中的所有目标"
  },
  {
    "id": "person",
    "name": "人员提示词",
    "prompt": "检测图像中的人员"
  },
  {
    "id": "vehicle",
    "name": "车辆提示词",
    "prompt": "检测图像中的车辆，包括汽车、卡车、摩托车等"
  }
]
```

## 5. 启动和运行指南

### 5.1 开发环境启动

#### 5.1.1 使用启动脚本(Windows)
```bash
# 双击运行start.bat文件
# 或在命令行执行:
start.bat
```

#### 5.1.2 手动启动

**启动后端服务:**
```bash
# 1. 激活虚拟环境
cd backend
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# 2. 启动Flask应用
python app.py

# 服务启动后显示:
# * Running on http://127.0.0.1:5000
# * Debug mode: on
```

**启动前端服务:**
```bash
# 新开终端窗口
cd frontend

# 启动React开发服务器
npm start

# 服务启动后自动打开浏览器:
# http://localhost:3000
```

### 5.2 生产环境部署

#### 5.2.1 前端构建
```bash
cd frontend

# 构建生产版本
npm run build

# 构建文件位于build/目录
ls -la build/
```

#### 5.2.2 使用Nginx部署前端
```nginx
# /etc/nginx/sites-available/mllm-frontend
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/frontend/build;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 5.2.3 使用Gunicorn部署后端
```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# 使用配置文件
gunicorn -c gunicorn.conf.py app:app
```

**gunicorn.conf.py配置:**
```python
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

### 5.3 Docker容器化部署

#### 5.3.1 后端Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
```

#### 5.3.2 前端Dockerfile
```dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 5.3.3 Docker Compose配置
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
    volumes:
      - ./models:/app/models
      - ./backend/.env:/app/.env
    
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
```

## 6. 系统验证和测试

### 6.1 服务状态检查

```bash
# 检查后端服务
curl http://localhost:5000/api/prompts

# 检查前端服务
curl http://localhost:3000

# 检查系统资源
ps aux | grep python
ps aux | grep node
```

### 6.2 功能验证测试

```bash
# 测试图像检测API
curl -X POST http://localhost:5000/api/detect \
  -F "image=@test_image.jpg" \
  -F "prompt=检测图像中的人员"

# 测试聊天API
curl -X POST http://localhost:5000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "你好"}'
```

### 6.3 性能监控

```bash
# 监控系统资源
htop  # 或 top

# 监控网络连接
netstat -tulpn | grep :5000
netstat -tulpn | grep :3000

# 监控日志
tail -f backend/app.log
```

## 7. 常见问题和解决方案

### 7.1 依赖安装问题

**问题**: PyTorch安装失败
```bash
# 解决方案: 使用清华源
pip install torch -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**问题**: npm安装缓慢
```bash
# 解决方案: 使用淘宝源
npm config set registry https://registry.npmmirror.com
npm install
```

### 7.2 模型加载问题

**问题**: 模型文件缺失
```bash
# 解决方案: 检查模型目录
ls -la models/grounding-dino-tiny/
# 确保包含所有必要文件
```

**问题**: CUDA内存不足
```bash
# 解决方案: 在.env中设置
DEVICE=cpu
# 或减少batch_size
```

### 7.3 API调用问题

**问题**: Qwen API调用失败
```bash
# 检查API密钥配置
echo $QWEN_API_KEY
# 检查网络连接
ping dashscope.aliyuncs.com
```

### 7.4 端口冲突问题

**问题**: 端口被占用
```bash
# 查找占用进程
lsof -i :5000  # Linux/macOS
netstat -ano | findstr :5000  # Windows

# 终止进程或更改端口
```

## 8. 维护和更新

### 8.1 日常维护

```bash
# 清理临时文件
rm -rf backend/temp/*

# 更新依赖
pip install --upgrade -r requirements.txt
npm update

# 备份配置
cp backend/.env backend/.env.backup
```

### 8.2 系统更新

```bash
# 拉取最新代码
git pull origin main

# 重新安装依赖
pip install -r requirements.txt
npm install

# 重启服务
systemctl restart mllm-backend
systemctl restart mllm-frontend
```

### 8.3 监控和日志

```bash
# 设置日志轮转
# /etc/logrotate.d/mllm
/path/to/backend/app.log {
    daily
    rotate 7
    compress
    missingok
    notifempty
}
```

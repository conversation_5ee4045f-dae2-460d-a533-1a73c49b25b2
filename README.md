# 多模态图像语义理解系统

基于浏览器-服务器架构的目标检测和态势分析系统

## 功能特性
- 现代、响应式的用户界面
- 拖放式图像上传
- 实时图像预览（缩放和平移）
- 目标检测与边界框显示
- 情境分析结果展示
- 支持多种图像格式（JPEG、PNG、WebP等）
- Qwen2.5-VL 7B多模态大模型集成分析

## 技术栈
- 前端：React + TypeScript
- 后端：Python Flask
- AI模型：多模态目标检测模型

## 项目结构
```
src/
├── frontend/     # 前端代码
└── backend/      # 后端代码
```


## Qwen API配置

要使用Qwen2.5-VL 7B多模态大模型功能，需要在`backend/.env`文件中配置以下环境变量：

```
QWEN_API_KEY=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
QWEN_API_URL=https://openrouter.ai/api/v1/chat/completions
```

1. 从OpenRouter获取API密钥
2. 将密钥填入`.env`文件的`QWEN_API_KEY`字段
3. 重启后端服务使配置生效

## API端点

### Qwen分析端点
- **URL**: `/api/qwen-analyze`
- **方法**: POST
- **参数**:
  - `image`: 图像文件（JPEG、PNG等格式）
  - `prompt`: 文本提示词
- **响应**: JSON格式的分析结果

```json
{
  "analysis": "图像内容的详细分析",
  "model": "qwen-vl-max"
}
```
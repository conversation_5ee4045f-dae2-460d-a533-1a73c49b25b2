import requests
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from PIL import Image, ImageDraw, ImageFont
import io
import torch
# from transformers import AutoImageProcessor, AutoModelForObjectDetection
from transformers import AutoProcessor, AutoModelForZeroShotObjectDetection
import os
from dotenv import load_dotenv
import json
import re
from datetime import datetime
import hashlib
import uuid
import base64
import logging
from urllib.parse import urljoin
from dashscope import MultiModalConversation

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

device = "cuda" if torch.cuda.is_available() else "cpu"
# 加载环境变量
load_dotenv()

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

# 加载预训练模型
# IMAGE_PROCESSOR = AutoImageProcessor.from_pretrained("IDEA-Research/grounding-dino-tiny")  # ("facebook/detr-resnet-50")
# MODEL = AutoModelForObjectDetection.from_pretrained("IDEA-Research/grounding-dino-tiny")
IMAGE_PROCESSOR = AutoProcessor.from_pretrained("../models/grounding-dino-tiny") 
MODEL = AutoModelForZeroShotObjectDetection.from_pretrained("../models/grounding-dino-tiny").to(device)

from torchvision.ops import box_iou

def box_IoU(scores, text_labels, boxes, iou_threshold=0.95):
    """
    改进版 IoU 过滤，严格处理大框包含小框的情况
    当大框包含小框或当两个框有较高IoU但无包含关系时，只保留小框
    
    参数:
        scores (Tensor): 检测框得分张量 (形状: [N])
        text_labels (list): 检测框文本标签列表 (长度: N)
        boxes (Tensor): 检测框坐标张量 [N, 4] (格式: [x0, y0, x1, y1])
        iou_threshold (float): IoU阈值，默认0.95
        
    返回:
        Tuple(Tensor, list, Tensor): 过滤后的scores, text_labels, boxes
    """
    if len(boxes) == 0:
        return scores, text_labels, boxes
    
    # 确保 boxes 是二维张量 [N, 4]
    if boxes.dim() == 1:
        boxes = boxes.unsqueeze(0)
    
    # 计算每个框的面积
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
    
    # 计算每个框的面积
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
    
    # 按面积升序排序，优先保留面积小的框
    sorted_indices = torch.argsort(areas)
    
    keep_indices = []
    
    for i in sorted_indices:
        box_i = boxes[i]
        keep = True
        
        # 检查当前框是否与任何已保留的框有高IoU重叠
        remove_indices = []
        for j in keep_indices:
            box_j = boxes[j]
            
            # 计算 IoU
            iou = box_iou(box_i.unsqueeze(0), box_j.unsqueeze(0)).item()
            
            # 判断包含关系
            i_contains_j = (box_i[0] <= box_j[0] and box_i[1] <= box_j[1] and 
                           box_i[2] >= box_j[2] and box_i[3] >= box_j[3])
            j_contains_i = (box_j[0] <= box_i[0] and box_j[1] <= box_i[1] and 
                           box_j[2] >= box_i[2] and box_j[3] >= box_i[3])
            
            # 计算当前框和已保留框的面积
            area_i = (box_i[2] - box_i[0]) * (box_i[3] - box_i[1])
            area_j = (box_j[2] - box_j[0]) * (box_j[3] - box_j[1])
            
            # 如果IoU超过阈值或存在包含关系
            if iou > iou_threshold or i_contains_j or j_contains_i:
                # 比较面积，保留小框
                if area_i < area_j:
                    # 当前框面积更小，移除已保留的框
                    remove_indices.append(j)
                else:
                    # 已保留框面积更小，跳过当前框
                    keep = False
                    break
        
        # 移除被当前框包含的已保留框
        for idx in remove_indices:
            keep_indices.remove(idx)
        
        if keep:
            keep_indices.append(i.item() if torch.is_tensor(i) else i)
    
    # 按原始顺序排序保留的索引
    keep_indices = sorted(keep_indices)
    
    return scores[keep_indices], [text_labels[i] for i in keep_indices], boxes[keep_indices]


@app.route('/api/detect', methods=['POST'])
def detect_objects():
    try:
        # 获取上传的文件和检测提示
        image_file = request.files.get('image')
        video_file = request.files.get('video')
        prompt = request.form.get('prompt', '')
        selectedTopics = request.form.get('selectedTopics', '')

        # print(f"Received image file: {image_file}")
        # print(f"Received video file: {video_file}")
        print(f"Received prompt: {prompt}")
        print(f"Received selectedTopics: {selectedTopics}")

        # 检查是否提供了文件
        if not image_file and not video_file:
            return jsonify({'error': '未提供图像或视频文件'}), 400

        # 处理图像文件
        if image_file:
            # 处理图像
            image = Image.open(io.BytesIO(image_file.read())).convert('RGB')
            # print(f"Image size: {image.size}")

            # 准备模型输入
            inputs = IMAGE_PROCESSOR(images=image, text=prompt, return_tensors="pt").to(device)

            # 运行目标检测
            with torch.no_grad():
                outputs = MODEL(**inputs)
            print(f"Model outputs: {outputs}")
            print(f"Model outputs logits: {outputs.logits}")
            print(f"Model outputs pred_boxes: {outputs.pred_boxes}")

            # 处理检测结果
            target_sizes = [image.size[::-1]]
            results = IMAGE_PROCESSOR.post_process_grounded_object_detection(
                outputs,
                inputs.input_ids,
                box_threshold=0.08,
                text_threshold=0.00,
                target_sizes=target_sizes
            )[0]
            print(f"Processed results: {results}")
            print(f"Processed results scores: {results['scores']}")
            print(f"Processed results labels: {results['text_labels']}")
            print(f"Processed results boxes: {results['boxes']}")
            scores = results['scores']
            text_labels = results['text_labels']
            boxes = results['boxes']

            # 应用IoU过滤
            scores, text_labels, boxes = box_IoU(
                scores=scores, 
                text_labels=text_labels, 
                boxes=boxes, 
                iou_threshold=0.95
                )
            print(f"Filtered results scores: {scores}")
            print(f"Filtered results labels: {text_labels}")
            print(f"Filtered results boxes: {boxes}")

            # 生成批次ID
            batch_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 获取侦察区域中心点坐标
            reconnaissance_center = request.form.get('reconnaissance_center')
            if reconnaissance_center:
                try:
                    reconnaissance_coordinates = json.loads(reconnaissance_center)
                    # 验证坐标格式
                    if isinstance(reconnaissance_coordinates, dict) and \
                       'longitude' in reconnaissance_coordinates and \
                       'latitude' in reconnaissance_coordinates:
                        longitude = reconnaissance_coordinates['longitude']
                        latitude = reconnaissance_coordinates['latitude']
                        # 验证坐标范围
                        if not (-180 <= longitude <= 180):
                            return jsonify({'error': '经度必须在-180到180之间'}), 400
                        if not (-90 <= latitude <= 90):
                            return jsonify({'error': '纬度必须在-90到90之间'}), 400
                    else:
                        return jsonify({'error': '侦察区域坐标格式不正确'}), 400
                except json.JSONDecodeError:
                    return jsonify({'error': '侦察区域坐标格式不正确'}), 400
            else:
                reconnaissance_coordinates = None
            
            # 格式化检测结果
            detections = []
            for index, (score, label, box) in enumerate(zip(
                results["scores"], results["text_labels"], results["boxes"]
            )):
                box = [round(i, 2) for i in box.tolist()]
                # 生成目标ID，使用批次ID和索引创建唯一哈希值
                target_id_string = f"{batch_id}_{index}"
                target_id = hashlib.md5(target_id_string.encode()).hexdigest()
                # print(f"Target ID: {target_id}")
                
                detections.append({
                    "class": label,
                    "score": score.tolist(),
                    "box": {
                        "xmin": box[0],
                        "ymin": box[1],
                        "xmax": box[2],
                        "ymax": box[3]
                    },
                    "target_id": target_id
                })
            # print(f"Detections: {detections}")

            # 生成检测结果图像
            result_image = draw_detections_on_image(image, detections)
            # print(f"Result image size: {result_image.size}")

            # 确保输出图像大小正确
            result_image = result_image.resize(image.size)
            # print(f"Resized result image size: {result_image.size}")

            # 确保输出为RGB
            if result_image.mode != 'RGB':
                result_image = result_image.convert('RGB')
            # print(f"Result image mode: {result_image.mode}")

            # 保存检测结果图像到内存
            img_io = io.BytesIO()
            result_image.save(img_io, 'PNG')
            img_io.seek(0)
            # print(f"Image IO size: {img_io.getbuffer().nbytes}")

            # 生成态势分析结果
            # 安全解析 JSON 字符串
            try:
                topics =  json.loads(selectedTopics)
            except json.JSONDecodeError:
                return jsonify({'error': 'selectedTopics格式不正确'}), 400
            situation_analysis = generate_situation_analysis(image, detections, topics)
            print(f"Topics: {topics}")
            print(f"Situation analysis: {situation_analysis}")
            
            # 在检测结果中包含侦察区域坐标
            if reconnaissance_coordinates:
                for detection in detections:
                    detection['reconnaissance_center'] = reconnaissance_coordinates

            return jsonify({
                "detections": detections,
                "situation_analysis": situation_analysis,
                "result_image": img_io.getvalue().hex()  # 将图像数据转换为十六进制字符串
            })
        
        # 处理视频文件
        elif video_file:
            # 验证视频文件类型
            allowed_video_types = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm']
            if video_file.content_type not in allowed_video_types:
                return jsonify({'error': '不支持的视频文件类型'}), 400
            
            # 保存视频文件到临时位置
            video_filename = f"temp_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
            video_path = os.path.join("temp", video_filename)
            
            # 确保temp目录存在
            os.makedirs("temp", exist_ok=True)
            
            # 保存视频文件
            video_file.save(video_path)
            
            # 返回视频文件路径
            return jsonify({
                "message": "视频文件已上传成功",
                "video_path": video_path
            })
        
    except Exception as e:
        return jsonify({'error': '文件处理失败', 'details': str(e)}), 500


@app.route('/api/video/<path:video_path>', methods=['GET'])
def serve_video(video_path):
    """提供视频文件服务"""
    try:
        # 检查文件是否存在
        full_path = os.path.join("temp", video_path)
        if not os.path.exists(full_path):
            return jsonify({'error': '视频文件不存在'}), 404
        
        # 返回视频文件
        return send_file(full_path, as_attachment=False)
    except Exception as e:
        return jsonify({'error': '视频文件服务失败', 'details': str(e)}), 500


def generate_situation_analysis(image, detections, selectedTopics):
    """生成态势分析结果"""
    situation_details = []
    if not detections:
        return {"summary": "未检测到任何目标", "details": []}

    # 统计检测到的目标类别
    class_counts = {}
    for det in detections:
        cls = det["class"]
        class_counts[cls] = class_counts.get(cls, 0) + 1

    # 生成分析摘要
    summary = f"检测到{len(detections)}个目标，包括"
    summary += ", ".join([f"{count}个{cls}" for cls, count in class_counts.items()])

    # # 如果有用户提示，加入相关分析
    # if prompt:
    #     summary += f"。用户提示: '{prompt}'"

    # 如果有用户选择的话题，加入相关分析
    if selectedTopics:
        summary += f"。用户选择的话题: {selectedTopics}"
        # 创建内存缓冲区
        buffer = io.BytesIO()
        # 将图像保存到缓冲区（指定格式）
        image.save(buffer, format='JPEG')  # 或 'PNG'
        # 获取完整的图像字节数据
        image_bytes = buffer.getvalue()
        # 调用Qwen API进行分析
        try:
            for topic in selectedTopics:
                analysis_info = call_qwen_api(
                image=image_bytes, 
                prompt=topic
                )
                situation_details.append({
                    "topic": topic,
                    "situation": analysis_info['analysis']
                })
            print(f"Qwen API response: {situation_details}")
        except Exception as e:
            logger.error(f"Qwen分析失败: {str(e)}")
            situation_details = [{"topic": topic, "situation": "分析失败"} for topic in selectedTopics]

    return {
        "summary": summary,
        "class_distribution": class_counts,
        # "selectedTopics": selectedTopics,
        "situation_details": situation_details,
        # "details": [
        #     f"{det['class']} (置信度: {det['score']:.2f})"
        #     for det in detections
        # ]
    }


def draw_detections_on_image(image, detections):
    """在图像上绘制检测框和标签"""
    draw = ImageDraw.Draw(image)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    for detection in detections:
        box = detection["box"]
        class_name = detection["class"]
        score = detection["score"]
        
        # 绘制检测框
        draw.rectangle([
            (box["xmin"], box["ymin"]),
            (box["xmax"], box["ymax"])
        ], outline="red", width=2)
        
        # 绘制标签
        label = f"{class_name} {score:.2f}"
        # 获取文本尺寸
        # text_width, text_height = draw.textsize(label, font=font)
        bbox = draw.textbbox((0,0), label, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        # 绘制标签背景
        draw.rectangle([
            (box["xmin"], box["ymin"] - text_height),
            (box["xmin"] + text_width, box["ymin"])
        ], fill="red")
        # 绘制标签文本
        draw.text((box["xmin"], box["ymin"] - text_height), label, fill="white", font=font)
    
    return image

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        # 获取请求数据
        print("进入chat")
        # 安全获取图片文件
        image_file = request.files.get('image')
        # data = request.get_json()
        # 获取文本消息
        user_message = request.form.get('message')
        # 获取检测结果（如果有）
        detection_results_str = request.form.get('detectionResults')
        # 安全解析JSON字符串
        try:
            detection_results = json.loads(detection_results_str) if detection_results_str else None
        except json.JSONDecodeError:
            detection_results = None
        
        # 处理图像文件
        if image_file and image_file.filename != '':
            # 处理图像
            image = Image.open(io.BytesIO(image_file.read())).convert('RGB')
        else:
            image = None  # 明确设置为None

        # 生成AI响应
        ai_response = generate_chat_response(image, user_message, detection_results)
        
        # 确保ai_response是字符串
        if not isinstance(ai_response, str):
            ai_response = str(ai_response)
        
        return jsonify({'reply': ai_response})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


def generate_chat_response(image, user_message, detection_results):
    """生成聊天响应"""
    print("进入generate_chat_response")
    # 调用qwen响应生成
    # 处理图像文件（增加空值检查）
    image_bytes = None
    if image is not None:
        # 创建内存缓冲区
        buffer = io.BytesIO()
        # 将图像保存到缓冲区（指定格式）
        image.save(buffer, format='JPEG')  # 或 'PNG'
        # 获取完整的图像字节数据
        image_bytes = buffer.getvalue()
    else:
        print("未接收到有效图像")

    response = ""
    
    # 如果有检测结果，基于检测结果生成响应
    if detection_results:
        # 统计检测到的目标类别
        class_counts = {}
        for det in detection_results:
            cls = det.get('class', '未知')
            class_counts[cls] = class_counts.get(cls, 0) + 1
        
        # 生成目标统计信息
        target_summary = "，".join([f"{count}个{cls}" for cls, count in class_counts.items()])
        
        # 根据用户消息内容生成不同响应
        if any(keyword in user_message.lower() for keyword in ['数量', '多少', '统计']):
            response = f"根据检测结果，图像中包含{target_summary}。"
        elif any(keyword in user_message.lower() for keyword in ['位置', '坐标']):
            # 提供第一个检测目标的位置信息
            if detection_results:
                first_obj = detection_results[0]
                box = first_obj.get('box', {})
                response = f"检测到的第一个{first_obj.get('class', '目标')}位于坐标(xmin={box.get('xmin', 0)}, ymin={box.get('ymin', 0)}, xmax={box.get('xmax', 0)}, ymax={box.get('ymax', 0)})。"
            elif any(keyword in user_message.lower() for keyword in ['你好', 'hello', 'hi', '数量', '多少', '统计', '位置', '坐标']):
                response = "您好！我是多模态图像语义理解系统的AI助手。请上传一张图片并进行目标检测，然后我们可以进一步讨论检测结果。"
                if any(keyword in user_message.lower() for keyword in ['帮助', 'help']):
                    response = "我可以帮助您分析图像中的目标。请上传一张图片，点击'开始检测理解'按钮，然后我们可以讨论检测结果。"
                else:
                    response = "未检测到任何目标。"
        else:
            response = f"检测到{len(detection_results)}个目标，包括{target_summary}。您能提供更多关于这些目标的信息吗？"
    else:
        # 没有检测结果时的默认响应
        if any(keyword in user_message.lower() for keyword in ['你好', 'hello', 'hi', '数量', '多少', '统计', '位置', '坐标']):
            response = "您好！我是多模态图像语义理解系统的AI助手。请上传一张图片并进行目标检测，然后我们可以进一步讨论检测结果。"
        elif any(keyword in user_message.lower() for keyword in ['帮助', '帮我', '帮助我', 'help']):
            response = "我可以帮助您分析图像中的目标。请上传一张图片，点击'开始检测理解'按钮，然后我们可以讨论检测结果。"
        else:
            qwen_response = call_qwen_api(
                image=image_bytes, 
                prompt=user_message
                )
            # 确保qwen_response是字符串
            if isinstance(qwen_response, dict) and 'analysis' in qwen_response:
                response = qwen_response['analysis']
            else:
                response = str(qwen_response)
    
    return response


# 提示词模板管理API
PROMPT_TEMPLATES_FILE = 'prompt_templates.json'

def load_prompt_templates():
    """加载提示词模板"""
    if os.path.exists(PROMPT_TEMPLATES_FILE):
        with open(PROMPT_TEMPLATES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []


def save_prompt_templates(templates):
    """保存提示词模板"""
    with open(PROMPT_TEMPLATES_FILE, 'w', encoding='utf-8') as f:
        json.dump(templates, f, ensure_ascii=False, indent=2)


@app.route('/api/prompts', methods=['GET'])
def get_prompt_templates():
    """获取所有提示词模板"""
    try:
        templates = load_prompt_templates()
        # 转换字段名以匹配前端期望的格式
        formatted_templates = [
            {
                'id': template['id'],
                'name': template['name'],
                'prompt': template['prompt']
            }
            for template in templates
        ]
        return jsonify(formatted_templates)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/prompts', methods=['POST'])
def create_prompt_template():
    """创建新的提示词模板"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        prompt = data.get('prompt', '').strip()
        
        # 数据验证
        if not name:
            return jsonify({'error': '模板名称不能为空'}), 400
        if not prompt:
            return jsonify({'error': '提示词内容不能为空'}), 400
        
        # 检查名称唯一性
        templates = load_prompt_templates()
        if any(template['name'] == name for template in templates):
            return jsonify({'error': '模板名称已存在'}), 400
        
        # 创建新模板
        new_template = {
            'name': name,
            'prompt': prompt
        }
        # 生成唯一ID
        new_template['id'] = str(uuid.uuid4())
        templates.append(new_template)
        save_prompt_templates(templates)
        # 转换字段名以匹配前端期望的格式
        new_template = {
            'id': new_template['id'],
            'name': new_template['name'],
            'prompt': new_template['prompt']
        }
        return jsonify(new_template), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/prompts/<template_id>', methods=['PUT'])
def update_prompt_template(template_id):
    """更新提示词模板"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        prompt = data.get('prompt', '').strip()

        # 数据验证
        if not name:
            return jsonify({'error': '模板名称不能为空'}), 400
        if not prompt:
            return jsonify({'error': '提示词内容不能为空'}), 400
        
        templates = load_prompt_templates()
        # 检查模板文件是否存在
        if not os.path.exists(PROMPT_TEMPLATES_FILE):
            return jsonify({'error': '提示词模板文件不存在'}), 404
        # 查找要更新的模板
        template_index = None
        for i, template in enumerate(templates):
            if template['id'] == template_id:
                template_index = i
                break
        
        # 检查模板是否存在
        if template_index is None:
            return jsonify({'error': '模板不存在'}), 404
        
        # 检查名称唯一性（排除当前模板）
        if any(template['name'] == name and template['id'] != template_id for template in templates):
            return jsonify({'error': '模板名称已存在'}), 400
        
        # 更新模板
        templates[template_index]['name'] = name
        templates[template_index]['prompt'] = prompt
        # 保存更新后的模板
        save_prompt_templates(templates)
        # 转换字段名以匹配前端期望的格式
        updated_template = {
            'id': templates[template_index]['id'],
            'name': templates[template_index]['name'],
            'prompt': templates[template_index]['prompt']
        }
        return jsonify(updated_template)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/prompts/<template_id>', methods=['DELETE'])
def delete_prompt_template(template_id):
    """删除提示词模板"""
    try:
        # 检查模板文件是否存在
        if not os.path.exists(PROMPT_TEMPLATES_FILE):
            return jsonify({'error': '提示词模板文件不存在'}), 404
        # 加载模板
        templates = load_prompt_templates()
        # 检查模板是否存在
        if not templates:
            return jsonify({'error': '提示词模板列表为空'}), 404
        # 查找要删除的模板
        template_index = None
        for i, template in enumerate(templates):
            if template['id'] == template_id:
                template_index = i
                break

        if template_index is None:
            return jsonify({'error': '模板不存在'}), 404
        # 转换字段名以匹配前端期望的格式
        deleted_template = {
            'id': templates[template_index]['id'],
            'name': templates[template_index]['name'],
            'prompt': templates[template_index]['prompt']
        }
        # 删除模板
        templates.pop(template_index)
        save_prompt_templates(templates)
        return jsonify(deleted_template)
    except Exception as e:
        app.logger.error(f"删除模板时发生错误: {str(e)}")
        return jsonify({'error': f"删除模板时发生错误: {str(e)}"}), 500


@app.route('/api/detection-results', methods=['GET'])
def get_detection_results():
    try:
        # 加载现有的检测结果
        if os.path.exists('detection_results.json'):
            with open('detection_results.json', 'r') as f:
                all_detections = json.load(f)
        else:
            all_detections = []
        
        return jsonify(all_detections)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/qwen-analyze', methods=['POST'])
def qwen_analyze():
    """
    Qwen2.5-VL 7B多模态大模型API分析端点
    接受图像和文本提示，调用Qwen在线API进行分析
    
    请求格式:
    - image: 图像文件（JPEG、PNG等格式）
    - prompt: 文本提示词
    
    返回格式:
    - JSON响应包含模型生成的分析结果
    """
    try:
        # 获取上传的文件和提示词
        image_file = request.files.get('image')
        prompt = request.form.get('prompt', '')
        
        # 检查是否提供了文件
        if not image_file:
            return jsonify({'error': '未提供图像文件'}), 400
        
        # 检查是否提供了提示词
        if not prompt:
            return jsonify({'error': '未提供提示词'}), 400
        
        # 读取图像文件
        image_bytes = image_file.read()
        
        # 调用Qwen API进行分析
        result = call_qwen_api(image_bytes, prompt)
        print(result['analysis'])
        # 返回结果
        return jsonify({'analysis': result['analysis']})
    except Exception as e:
        logger.error(f"Qwen分析失败: {str(e)}")
        return jsonify({'error': 'Qwen分析失败', 'details': str(e)}), 500


def call_qwen_api(image, prompt):
    """
    调用Qwen2.5-VL 7B在线API
    
    Args:
        image (bytes): 图像文件的字节数据
        prompt (str): 文本提示词
        
    Returns:
        dict: API响应结果
    """
    # 获取API配置
    print("进入call_qwen_api")
    qwen_api_key = os.getenv('QWEN_API_KEY')
    qwen_api_url = os.getenv('QWEN_API_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    # qwen_api_url = os.getenv('QWEN_API_URL', 'https://openrouter.ai/api/v1/chat/completions')
    
    # 检查API密钥
    if not qwen_api_key:
        raise ValueError('QWEN_API_KEY环境变量未设置')
    
    # 将图像转换为base64编码
    image_base64 = base64.b64encode(image).decode('utf-8')
    
    # 构造API请求数据
    headers = {
        'Authorization': f'Bearer {qwen_api_key}',
        'Content-Type': 'application/json'
    }

    # try:
    ### 利用openRouter key和requests调用Qwen API ###
    #     response = requests.post(
    #         url=qwen_api_url,
    #         headers=headers,
    #         data=json.dumps({
    #             "model": "qwen/qwen2.5-vl-32b-instruct:free",
    #             "messages": [
    #                 {
    #                     "role": "system",
    #                     "content": [{"text": "You are a helpful assistant."}]
    #                 },
    #                 {
    #                     "role": "user",
    #                     "content": [
    #                     {
    #                         "type": "text",
    #                         "text": f'请分析这张图片中关于{prompt}的相关信息。'
    #                     },
    #                     {
    #                         "type": "image_url",
    #                         "image_url": {
    #                             "url": f"data:image/jpeg;base64,{image_base64}"
    #                         }
    #                     }
    #                     ]
    #                 }
    #             ],       
    #         }),
    #         timeout=(20, 50),  # 关键：增加超时时间
    #         proxies={"http": None, "https": None}  # 明确禁用代理
    #     )
    #     response.raise_for_status()
    #     result = response.json()['choices'][0]['message']['content']
    #     # 提取生成的文本
    #     if 'choices' in result and 'message' in result['choices'][0] and 'content' in result['choices'][0]['message'] and 'text' in result['choices'][0]['message']['content'][0]:
    #         return {
    #             'analysis': result['choices'][0]['message']['content'][0]['text'],
    #             'model': 'qwen/qwen2.5-vl-32b-instruct:free'
    #         }
    #     else:
    #         raise Exception("API响应格式错误")

    # except requests.exceptions.RequestException as e:
    #     print(f"请求失败: {str(e)}")
    #     if "10054" in str(e):
    #         print("错误 10054: 连接被远程主机强制关闭")
    #     raise e

    try:
        ### 利用阿里百灵key和dashscope调用Qwen API ###
        # 构造请求体
        payload = {
            "stream": False,
            "model": "qwen2.5-vl-32b-instruct",
            "messages": [
                {
                    "role": "system",
                    "content": [{"text": "You are a helpful assistant."}]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "image": f"data:image/jpeg;base64,{image_base64}"
                        },
                        {
                            "text": f'请分析这张图片中关于{prompt}的相关信息，输出结果为中文，字数不超过20。'
                            # "text": prompt
                        }
                    ]
                }
            ]
        }

        # 发送API请求
        response = MultiModalConversation.call(
            api_key=qwen_api_key,
            model=payload["model"],
            messages=payload["messages"],
            # stream=False,
            # parameters=payload["parameters"]
            timeout=(30, 60),  # 关键：增加超时时间
            proxies={"http": None, "https": None}  # 明确禁用代理
        )
        print("response:", response)
        # for chunk in response:
        #     print("chunk:", chunk)
        # print(f"Qwen API response: {response.json()}")
        # 检查响应状态
        if response.status_code != 200:
            raise Exception(f"Qwen API请求失败: {response.status_code} - {response.text}")
        
        # 解析响应
        result = response

        # # 提取生成的文本
        if 'output' in result and 'choices' in result['output'] and 'message' in result['output']['choices'][0] and 'content' in result['output']['choices'][0]['message'] and 'text' in result['output']['choices'][0]['message']['content'][0]:
            return {
                'analysis': result['output']['choices'][0]['message']['content'][0]['text'],
                'model': 'qwen2.5-vl-32b-instruct'
            }
        else:
            raise Exception(f"API响应格式不正确: {result}")
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        if "10054" in str(e):
            print("错误 10054: 连接被远程主机强制关闭")
        raise e

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
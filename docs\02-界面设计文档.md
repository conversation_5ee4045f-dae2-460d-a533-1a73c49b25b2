# 多模态图像语义理解系统 - 界面设计文档

## 1. 界面设计概述

本系统采用现代化的响应式Web界面设计，以深色主题为主，支持明暗主题切换。界面布局采用三栏式设计，左侧为控制面板，中间为图像预览和检测结果展示区域，右侧为结果列表和分析数据。

### 1.1 设计原则
- **简洁性**: 界面简洁明了，突出核心功能
- **一致性**: 统一的视觉风格和交互模式
- **可用性**: 符合用户操作习惯，降低学习成本
- **响应性**: 适配不同屏幕尺寸和设备
- **可访问性**: 支持键盘导航和屏幕阅读器

### 1.2 视觉风格
- **主色调**: 深蓝色系 (#1a1a2e, #16213e)
- **强调色**: 蓝色 (#0066cc) 和绿色 (#28a745)
- **文字色**: 白色 (#ffffff) 和浅灰色 (#e0e0e0)
- **边框色**: 深灰色 (#333333)
- **字体**: 系统默认字体栈，支持中英文

## 2. 用户界面布局设计

### 2.1 整体布局结构

```
┌─────────────────────────────────────────────────────────────────┐
│                        顶部标题栏                                │
│  [Logo] 多模态图像语义理解系统                    [设置按钮]      │
├─────────────────────────────────────────────────────────────────┤
│        │                    │                                   │
│  左侧   │                    │              右侧                 │
│  控制   │       中间         │              结果                 │
│  面板   │    预览展示区       │              展示区               │
│        │                    │                                   │
│        │                    │                                   │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 左侧控制面板 (300-600px宽度)

#### 2.2.1 侦察区域设置
- **经纬度输入框**: 数字输入，支持小数点
- **应用坐标按钮**: 验证并应用坐标设置
- **当前坐标显示**: 实时显示当前设置的坐标

#### 2.2.2 检测控制区域
- **提示词模板选择**: 下拉选择框，预设常用检测类型
- **提示词库管理按钮**: 打开提示词管理模态框
- **自定义提示词输入**: 多行文本框，支持自定义检测目标
- **态势关注主题**: 复选框组，支持多选

#### 2.2.3 检测按钮
- **开始检测理解按钮**: 主要操作按钮，带加载状态
- **加载动画**: 检测过程中显示旋转图标和"检测中..."文字

#### 2.2.4 聊天交互区域
- **聊天历史**: 滚动区域，显示用户和AI的对话记录
- **消息输入框**: 单行文本输入，支持回车发送
- **图片上传按钮**: 支持在聊天中上传图片
- **发送按钮**: 发送消息，带发送状态指示

### 2.3 中间预览展示区 (自适应宽度)

#### 2.3.1 文件上传区域
- **图像上传区**: 拖拽上传区域，支持点击选择
- **视频上传区**: 独立的视频上传区域
- **上传提示**: 清晰的拖拽和点击提示文字
- **文件格式说明**: 支持的文件格式列表

#### 2.3.2 输入预览区域
- **图像预览**: 支持缩放、平移的图像查看器
- **视频预览**: 视频播放器，支持播放控制
- **截图检测按钮**: 视频模式下的帧截取功能
- **预览控制**: 放大、缩小、重置按钮

#### 2.3.3 检测结果展示区域
- **结果图像**: 带检测框的图像展示
- **缩放控制**: 独立的缩放和平移控制
- **检测框标注**: 彩色边框和标签显示

### 2.4 右侧结果展示区 (固定宽度)

#### 2.4.1 检测目标列表
- **表格展示**: 序号、批次、目标ID、类型、位置、置信度
- **批量操作**: 全选/取消全选、批量发送
- **单项操作**: 单个目标的选择和发送
- **分页控制**: 支持大量结果的分页展示

#### 2.4.2 态势分析结果
- **分析表格**: 序号、态势项、态势详情
- **批量操作**: 支持批量选择和发送
- **详情展示**: 完整的分析结果文本

## 3. 交互流程设计

### 3.1 图像检测流程

```
用户操作流程:
1. 拖拽或点击上传图像文件
2. 图像在预览区域显示
3. 选择或输入检测提示词
4. 选择态势关注主题(可选)
5. 点击"开始检测理解"按钮
6. 等待检测完成(显示加载状态)
7. 查看检测结果和态势分析
8. 可选择结果进行批量或单个发送
```

### 3.2 视频检测流程

```
用户操作流程:
1. 拖拽或点击上传视频文件
2. 视频在预览区域播放
3. 播放到目标帧位置
4. 点击"截图检测"按钮
5. 系统截取当前帧进行检测
6. 后续流程同图像检测
```

### 3.3 聊天交互流程

```
用户操作流程:
1. 在聊天输入框输入问题
2. 可选择上传图片(通过上传按钮)
3. 点击发送按钮或按回车键
4. 消息添加到聊天历史
5. 等待AI回复(显示加载状态)
6. AI回复显示在聊天历史中
7. 可继续进行多轮对话
```

### 3.4 系统设置流程

```
用户操作流程:
1. 点击右上角设置按钮
2. 打开系统设置模态框
3. 可修改页面标题、Logo、主题
4. 保存设置并关闭模态框
5. 设置立即生效并持久化保存
```

## 4. 响应式设计说明

### 4.1 断点设计

| 屏幕尺寸 | 断点 | 布局调整 |
|----------|------|----------|
| 大屏幕 | ≥1200px | 三栏布局，左侧面板360px |
| 中等屏幕 | 768px-1199px | 三栏布局，左侧面板300px |
| 小屏幕 | <768px | 单栏布局，面板折叠 |

### 4.2 布局适配策略

#### 4.2.1 大屏幕适配 (≥1200px)
- 左侧控制面板宽度: 360px
- 中间预览区域: 自适应剩余空间
- 右侧结果区域: 固定宽度
- 所有功能区域完全展开

#### 4.2.2 中等屏幕适配 (768px-1199px)
- 左侧控制面板宽度: 300px
- 压缩部分间距和内边距
- 保持三栏布局结构
- 字体大小适当调整

#### 4.2.3 小屏幕适配 (<768px)
- 改为垂直堆叠布局
- 左侧面板变为顶部折叠菜单
- 预览区域占满屏幕宽度
- 结果区域移至底部
- 触摸友好的按钮尺寸

### 4.3 组件响应式特性

#### 4.3.1 可调整分隔器
- 支持拖拽调整左侧面板宽度
- 最小宽度: 200px
- 最大宽度: 600px
- 实时预览调整效果

#### 4.3.2 图像预览组件
- 自适应容器尺寸
- 支持触摸手势缩放
- 保持图像宽高比
- 响应式控制按钮

## 5. 用户体验优化方案

### 5.1 加载状态优化

#### 5.1.1 检测过程反馈
- 检测按钮显示加载动画
- 按钮文字变为"检测中..."
- 禁用重复提交
- 进度指示器(如适用)

#### 5.1.2 聊天发送反馈
- 发送按钮显示加载状态
- 消息立即显示在历史中
- AI回复前显示"正在思考..."
- 网络错误时显示重试选项

### 5.2 交互反馈优化

#### 5.2.1 拖拽上传反馈
- 拖拽悬停时高亮边框
- 文件类型验证提示
- 上传进度显示
- 成功/失败状态反馈

#### 5.2.2 按钮交互反馈
- 悬停状态变化
- 点击动画效果
- 禁用状态明确显示
- 操作成功的视觉确认

### 5.3 错误处理优化

#### 5.3.1 友好错误提示
- 明确的错误信息描述
- 建议的解决方案
- 重试操作选项
- 错误状态的视觉区分

#### 5.3.2 表单验证优化
- 实时输入验证
- 清晰的验证错误提示
- 必填字段标识
- 输入格式说明

### 5.4 性能优化

#### 5.4.1 图像加载优化
- 图像懒加载
- 缩略图预览
- 渐进式加载
- 加载失败占位符

#### 5.4.2 列表渲染优化
- 虚拟滚动(大数据量时)
- 分页加载
- 搜索过滤功能
- 排序功能

### 5.5 可访问性优化

#### 5.5.1 键盘导航
- Tab键顺序合理
- 快捷键支持
- 焦点状态明确
- 跳过链接功能

#### 5.5.2 屏幕阅读器支持
- 语义化HTML标签
- ARIA标签补充
- 图片alt文本
- 表单标签关联

### 5.6 主题和个性化

#### 5.6.1 主题切换
- 明暗主题无缝切换
- 主题偏好记忆
- 系统主题跟随
- 自定义主题色彩

#### 5.6.2 界面定制
- 可调整面板宽度
- 自定义页面标题
- 自定义Logo上传
- 布局偏好保存

## 6. 界面组件规范

### 6.1 按钮规范
- **主要按钮**: 蓝色背景，白色文字，圆角4px
- **次要按钮**: 透明背景，蓝色边框，蓝色文字
- **危险按钮**: 红色背景，白色文字
- **禁用按钮**: 灰色背景，深灰色文字

### 6.2 表单控件规范
- **输入框**: 深色背景，白色文字，蓝色焦点边框
- **选择框**: 与输入框样式一致
- **复选框**: 自定义样式，蓝色选中状态
- **文本域**: 可调整大小，最小高度80px

### 6.3 表格规范
- **表头**: 深色背景，白色文字，粗体
- **表格行**: 交替背景色，悬停高亮
- **操作列**: 右对齐，按钮组合
- **选择列**: 左对齐，复选框

### 6.4 模态框规范
- **背景遮罩**: 半透明黑色
- **内容区域**: 白色背景(明主题)或深色背景(暗主题)
- **关闭按钮**: 右上角X按钮
- **操作按钮**: 底部右对齐
